<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Talent Builders</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Sora:wght@100..800&display=swap"
      rel="stylesheet"
    />
    <style>
      /* Form4 Scoped CSS Styles */

      /* Apply box-sizing to all form elements to prevent overflow */
      .form4-container,
      .form4-container *,
      .form4-input,
      .form4-textarea,
      .form4-select,
      .form4-checkbox,
      .form4-submit {
        box-sizing: border-box;
      }

      .form4-container {
        width: 100%;
        max-width: 100%;
        font-family: "Sora", sans-serif;
        overflow-x: hidden;
      }

      .form4-row {
        display: grid;
        margin-bottom: 0.5rem;
        width: 100%;
      }

      .form4-row-flex {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 0.5rem;
        width: 100%;
      }

      .form4-label {
        font-weight: 500;
        padding-left: 0.5rem;
        color: #717e8a;
      }

      .form4-input {
        background-color: #e9eaed;
        color: #111827;
        font-size: 0.875rem;
        border-radius: 0.75rem;
        display: block;
        width: 100%;
        max-width: 100%;
        padding: 0.625rem;
        border: 2px solid transparent;
        outline: none;
        transition: all 0.15s ease-in-out;
        box-sizing: border-box;
      }

      .form4-input:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }

      .form4-textarea {
        background-color: #e9eaed;
        color: #111827;
        font-size: 0.875rem;
        border-radius: 0.75rem;
        display: block;
        width: 100%;
        max-width: 100%;
        padding: 0.625rem;
        resize: none;
        height: 6rem;
        min-height: 6rem;
        max-height: 6rem;
        border: 2px solid transparent;
        outline: none;
        transition: all 0.15s ease-in-out;
        box-sizing: border-box;
      }

      .form4-textarea:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }

      .form4-select {
        background-color: #e9eaed;
        color: #111827;
        font-size: 0.875rem;
        border-radius: 0.75rem;
        display: block;
        width: 100%;
        max-width: 100%;
        padding: 0.625rem;
        appearance: none;
        cursor: pointer;
        border: 2px solid transparent;
        outline: none;
        transition: all 0.15s ease-in-out;
        box-sizing: border-box;
      }

      .form4-select:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }

      .form4-checkbox {
        background-color: #e9eaed;
        color: #111827;
        font-size: 0.875rem;
        border-radius: 0.5rem;
        display: block;
        height: 1.25rem;
        width: 1.25rem;
        appearance: none;
        border: 2px solid transparent;
        outline: none;
        cursor: pointer;
        transition: all 0.15s ease-in-out;
        box-sizing: border-box;
        flex-shrink: 0;
      }

      .form4-checkbox:focus {
        border-color: #ff8623;
        box-shadow: 0 0 0 3px rgba(255, 134, 35, 0.1);
      }

      .form4-checkbox:checked {
        background-color: #ff8623;
      }

      .form4-submit {
        background-color: #ff8623;
        color: white;
        font-weight: bold;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        margin-top: 0.5rem;
        width: 100%;
        max-width: 100%;
        appearance: none;
        cursor: pointer;
        border: none;
        outline: none;
        transition: all 0.3s ease;
        box-sizing: border-box;
      }

      .form4-submit:hover {
        background-color: #e5751f;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
          0 2px 4px -1px rgba(0, 0, 0, 0.06);
      }

      /*
       * All styles above are scoped with form4- prefix to ensure they only apply
       * to this specific form and don't affect other parts of the website
       */
    </style>
  </head>
  <body>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
    <form
      id="__vtigerWebForm57a9d7150d0a1a290331b801b0e2a933"
      class="__vtigerWebForm"
      name="Internship"
      action="https://microsipmtz.mantic360.net/modules/Webforms/capture.php"
      method="post"
      accept-charset="utf-8"
      enctype="multipart/form-data"
    >
      <input
        type="hidden"
        name="__vtrftk"
        value="sid:88911b6470ce18d0e7122d9dd4dea8ac241633b1,1731525291"
      /><input
        type="hidden"
        name="publicid"
        value="57a9d7150d0a1a290331b801b0e2a933"
      /><input type="hidden" name="urlencodeenable" value="1" /><input
        type="hidden"
        name="name"
        value="Internship"
      />
      <table class="form4-container">
        <tbody>
          <tr class="form4-row">
            <td>
              <label class="form4-label">Nombres*</label>
            </td>
            <td>
              <input
                type="text"
                name="firstname"
                data-label=""
                value=""
                required=""
                class="form4-input"
              />
            </td>
          </tr>
          <tr class="form4-row">
            <td>
              <label class="form4-label">Apellidos*</label>
            </td>
            <td>
              <input
                type="text"
                name="lastname"
                data-label=""
                value=""
                required=""
                class="form4-input"
              />
            </td>
          </tr>
          <tr class="form4-row">
            <td>
              <label class="form4-label">E-mail*</label>
            </td>
            <td>
              <input
                type="email"
                name="email"
                data-label=""
                value=""
                required=""
                class="form4-input"
              />
            </td>
          </tr>
          <tr class="form4-row">
            <td>
              <label class="form4-label">Teléfono móvil*</label>
            </td>
            <td>
              <input
                type="tel"
                name="mobile"
                data-label=""
                value=""
                pattern="[0-9]{10}"
                title="Por favor ingrese un número telefónico válido de 10 dígitos"
                required=""
                class="form4-input"
              />
            </td>
          </tr>
          <tr class="form4-row">
            <td>
              <label class="form4-label">Ciudad o Municipio*</label>
            </td>
            <td>
              <input
                type="text"
                name="cf_1450"
                data-label=""
                value=""
                required=""
                class="form4-input"
              />
            </td>
          </tr>
          <tr class="form4-row">
            <td>
              <label class="form4-label">Estado*</label>
            </td>
            <td>
              <select
                name="cf_1448"
                data-label="label:Estado"
                required=""
                class="form4-select"
              >
                <option value="">Seleccione un estado</option>
                <option value="Aguascalientes">Aguascalientes</option>
                <option value="Baja California">Baja California</option>
                <option value="Baja California Sur">Baja California Sur</option>
                <option value="Campeche">Campeche</option>
                <option value="Coahuila">Coahuila</option>
                <option value="Colima">Colima</option>
                <option value="Chiapas">Chiapas</option>
                <option value="Chihuahua">Chihuahua</option>
                <option value="CDMX">CDMX</option>
                <option value="Durango">Durango</option>
                <option value="Guanajuato">Guanajuato</option>
                <option value="Guerrero">Guerrero</option>
                <option value="Hidalgo">Hidalgo</option>
                <option value="Jalisco">Jalisco</option>
                <option value="Edo. de México">Edo. de México</option>
                <option value="Michoacan">Michoacan</option>
                <option value="Morelos">Morelos</option>
                <option value="Nayarit">Nayarit</option>
                <option value="Nuevo León">Nuevo León</option>
                <option value="Oaxaca">Oaxaca</option>
                <option value="Puebla">Puebla</option>
                <option value="Querétaro">Querétaro</option>
                <option value="Quintana Roo">Quintana Roo</option>
                <option value="San Luis Potosí">San Luis Potosí</option>
                <option value="Sinaloa">Sinaloa</option>
                <option value="Sonora">Sonora</option>
                <option value="Tabasco">Tabasco</option>
                <option value="Tamaulipas">Tamaulipas</option>
                <option value="Tlaxcala">Tlaxcala</option>
                <option value="Veracruz">Veracruz</option>
                <option value="Yucatán">Yucatán</option>
                <option value="Zacatecas">Zacatecas</option>
              </select>
            </td>
          </tr>
          <tr class="form4-row">
            <td>
              <label class="form4-label">Carrera*</label>
            </td>
            <td>
              <input
                type="text"
                name="cf_1470"
                data-label=""
                value=""
                required=""
                class="form4-input"
              />
            </td>
          </tr>
          <tr class="form4-row">
            <td>
              <label class="form4-label">Semestre*</label>
            </td>
            <td>
              <select
                name="cf_1474"
                data-label="label:Semestre"
                required=""
                class="form4-select"
              >
                <option value="">Seleccionar valor</option>
                <option value="1">1</option>
                <option value="2">2</option>
                <option value="3">3</option>
                <option value="4">4</option>
                <option value="5">5</option>
                <option value="6">6</option>
                <option value="7">7</option>
                <option value="8">8</option>
                <option value="9">9</option>
                <option value="10">10</option>
                <option value="11">11</option>
                <option value="12">12</option>
                <option value="Otro">Otro</option>
              </select>
            </td>
          </tr>
          <tr class="form4-row">
            <td>
              <label class="form4-label">Universidad*</label>
            </td>
            <td>
              <input
                type="text"
                name="cf_1476"
                data-label=""
                value=""
                required=""
                class="form4-input"
              />
            </td>
          </tr>
          <tr class="form4-row">
            <td>
              <label class="form4-label"
                >¿Como tus habilidades y fortalezas pueden aportar al
                crecimiento de Microsip?*</label
              >
            </td>
            <td>
              <textarea
                name="cf_1468"
                required=""
                class="form4-textarea"
              ></textarea>
            </td>
          </tr>
          <tr class="form4-row-flex">
            <td>
              <label for="recibir-correos" class="form4-label"
                >Recibir correos de Microsip</label
              >
            </td>
            <td>
              <input
                type="hidden"
                name="cf_1454"
                data-label=""
                value="0"
              /><input
                type="checkbox"
                name="cf_1454"
                data-label=""
                value="1"
                class="form4-checkbox"
                id="recibir-correos"
              />
            </td>
          </tr>
          <tr>
            <td>
              <select name="cf_1460" data-label="label:Procedencia" hidden="">
                <option value="">Seleccionar valor</option>
                <option value="Bolsa de Trabajo">Bolsa de Trabajo</option>
                <option value="Internship" selected="">Internship</option>
                <option value="Pruebas">Pruebas</option>
                <option value="Duplicado">Duplicado</option>
              </select>
            </td>
          </tr>
          <tr class="form4-row">
            <td>
              <label class="form4-label">Curriculum *</label>
            </td>
            <td>
              <input
                type="file"
                name="file_17_1"
                required="required"
                class="form4-input"
              />
            </td>
          </tr>
        </tbody>
      </table>
      <script
        src="https://www.google.com/recaptcha/api.js?hl=es"
        async
        defer
      ></script>
      <div
        class="g-recaptcha"
        data-sitekey="6LfXMKsZAAAAACGRuPAKqie7am6XkoKq0-tZz-aJ"
      ></div>
      <input
        type="hidden"
        id="captchaUrl"
        value="https://microsipmtz.mantic360.net/modules/Settings/Webforms/actions/CheckCaptcha.php"
      /><input
        type="hidden"
        name="recaptcha_validation_value"
        id="recaptcha_validation_value"
        value="false"
      /><input type="submit" value="Enviar" class="form4-submit" />
    </form>
    <script type="text/javascript">
      var _form_active = "";
      window.onload = function () {
        var N = navigator.appName,
          ua = navigator.userAgent,
          tem;
        var M = ua.match(
          /(opera|chrome|safari|firefox|msie)\/?\s*(\.?\d+(\.\d+)*)/i
        );
        if (M && (tem = ua.match(/version\/([\.\d]+)/i)) != null) M[2] = tem[1];
        M = M ? [M[1], M[2]] : [N, navigator.appVersion, "-?"];
        var browserName = M[0];
        var _forms = document.getElementsByClassName("__vtigerWebForm");
        for (var i = 0; i < _forms.length; i++) {
          var form = _forms[i];
          let inputs = form.elements;
          form.onsubmit = function () {
            let _formid = "#" + this.id;
            let _formidname = this.id;
            _form_active = this.id;
            var required = [],
              att,
              val;
            for (var i = 0; i < inputs.length; i++) {
              att = inputs[i].getAttribute("required");
              val = inputs[i].value;
              type = inputs[i].type;
              if (type == "email") {
                if (val != "") {
                  var elemLabel = inputs[i].getAttribute("label");
                  var emailFilter =
                    /^[_/a-zA-Z0-9]+([!"#$%&()*+,./:;<=>?\^_`{|}~-]?[a-zA-Z0-9/_/-])*@[a-zA-Z0-9]+([\_\-\.]?[a-zA-Z0-9]+)*\.([\-\_]?[a-zA-Z0-9])+(\.?[a-zA-Z0-9]+)?$/;
                  var illegalChars = /[\(\)\<\>\,\;\:\"\[\]]/;
                  if (!emailFilter.test(val)) {
                    alert(
                      "For " +
                        elemLabel +
                        " field please enter valid email address"
                    );
                    return false;
                  } else if (val.match(illegalChars)) {
                    alert(elemLabel + " field contains illegal characters");
                    return false;
                  }
                }
              }
              if (att != null) {
                if (val.replace(/^\s+|\s+$/g, "") == "") {
                  required.push(inputs[i].getAttribute("label"));
                }
              }
            }
            if (required.length > 0) {
              alert("The following fields are required: " + required.join());
              return false;
            }
            var numberTypeInputs =
              document.querySelectorAll("input[type=number]");
            for (var i = 0; i < numberTypeInputs.length; i++) {
              val = numberTypeInputs[i].value;
              var elemLabel = numberTypeInputs[i].getAttribute("label");
              var elemDataType = numberTypeInputs[i].getAttribute("datatype");
              if (val != "") {
                if (elemDataType == "double") {
                  var numRegex = /^[+-]?\d+(\.\d+)?$/;
                } else {
                  var numRegex = /^[+-]?\d+$/;
                }
                if (!numRegex.test(val)) {
                  alert(
                    "For " + elemLabel + " field please enter valid number"
                  );
                  return false;
                }
              }
            }
            var dateTypeInputs = document.querySelectorAll("input[type=date]");
            for (var i = 0; i < dateTypeInputs.length; i++) {
              dateVal = dateTypeInputs[i].value;
              var elemLabel = dateTypeInputs[i].getAttribute("label");
              if (dateVal != "") {
                var dateRegex =
                  /^[1-9][0-9]{3}-(0[1-9]|1[0-2]|[1-9]{1})-(0[1-9]|[1-2][0-9]|3[0-1]|[1-9]{1})$/;
                if (!dateRegex.test(dateVal)) {
                  alert(
                    "For " +
                      elemLabel +
                      " field please enter valid date in required format"
                  );
                  return false;
                }
              }
            }
            var inputElems = document.getElementsByTagName("input");
            var totalFileSize = 0;
            for (var i = 0; i < inputElems.length; i++) {
              if (inputElems[i].type.toLowerCase() === "file") {
                var file = inputElems[i].files[0];
                if (typeof file !== "undefined") {
                  var totalFileSize = totalFileSize + file.size;
                }
              }
            }
            if (totalFileSize > 52428800) {
              alert("Maximum allowed file size including all files is 50MB.");
              return false;
            }
            var getStatus = false;
            var recaptchaValidationValue = document.querySelector(
              _formid + " .g-recaptcha .g-recaptcha-response"
            ).value;
            var captchaUrl = document.querySelector(
              _formid + " #captchaUrl"
            ).value;
            var url =
              captchaUrl +
              "?form=" +
              _formidname +
              "&recaptcha_response=" +
              recaptchaValidationValue;
            url = url + "&callback=JSONPCallback";
            jsonp.fetch(url);
            if (getStatus == false) {
              return false;
            }
          };
        }
      };
      var jsonp = {
        callbackCounter: 0,
        fetch: function (url) {
          url = url + "&callId=" + this.callbackCounter;
          var scriptTag = document.createElement("SCRIPT");
          scriptTag.src = url;
          scriptTag.async = true;
          scriptTag.id = "JSONPCallback_" + this.callbackCounter;
          scriptTag.type = "text/javascript";
          document.getElementsByTagName("HEAD")[0].appendChild(scriptTag);
          this.callbackCounter++;
        },
      };
      function JSONPCallback(data) {
        if (data.result.success == true) {
          document.querySelector(
            "#" + _form_active + " #recaptcha_validation_value"
          ).value = true;
          var form = document.getElementById(_form_active);
          form.submit();
        } else {
          document.querySelector(
            "#" + _form_active + " #recaptcha_validation_value"
          ).value = false;
          alert("Bot detectado");
        }
        var element = document.getElementById(
          "JSONPCallback_" + data.result.callId
        );
        element.parentNode.removeChild(element);
      }
    </script>
  </body>
</html>
