<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
      Precios - Sistema ERP - Contacta a un Partner - Contactanos - Adquiere
      Microsip
    </title>
  </head>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link
    href="https://fonts.googleapis.com/css2?family=Sora:wght@100..800&display=swap"
    rel="stylesheet"
  />
  <style>
    /* Form1 Scoped CSS Styles */

    /* Apply box-sizing to all form elements to prevent overflow */
    .form1-container,
    .form1-container *,
    .form1-input,
    .form1-textarea,
    .form1-select,
    .form1-checkbox,
    .form1-submit {
      box-sizing: border-box;
    }

    .form1-container {
      width: 100%;
      max-width: 100%;
      font-family: "Sora", sans-serif;
      overflow-x: hidden;
    }

    .form1-row {
      display: grid;
      margin-bottom: 0.5rem;
      width: 100%;
    }

    .form1-row-flex {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 0.5rem;
      width: 100%;
    }

    .form1-label {
      font-weight: 500;
      padding-left: 0.5rem;
      color: #717e8a;
    }

    .form1-input {
      background-color: #e9eaed;
      color: #111827;
      font-size: 0.875rem;
      border-radius: 0.75rem;
      display: block;
      width: 100%;
      max-width: 100%;
      padding: 0.625rem;
      border: 2px solid transparent;
      outline: none;
      transition: all 0.15s ease-in-out;
      box-sizing: border-box;
    }

    .form1-input:focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .form1-textarea {
      background-color: #e9eaed;
      color: #111827;
      font-size: 0.875rem;
      border-radius: 0.75rem;
      display: block;
      width: 100%;
      max-width: 100%;
      padding: 0.625rem;
      resize: none;
      height: 6rem;
      min-height: 6rem;
      max-height: 6rem;
      appearance: none;
      border: 2px solid transparent;
      outline: none;
      transition: all 0.15s ease-in-out;
      box-sizing: border-box;
    }

    .form1-textarea:focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .form1-select {
      background-color: #e9eaed;
      color: #111827;
      font-size: 0.875rem;
      border-radius: 0.75rem;
      display: block;
      width: 100%;
      max-width: 100%;
      padding: 0.625rem;
      appearance: none;
      cursor: pointer;
      border: 2px solid transparent;
      outline: none;
      transition: all 0.15s ease-in-out;
      box-sizing: border-box;
    }

    .form1-select:focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .form1-checkbox {
      background-color: #e9eaed;
      color: #111827;
      font-size: 0.875rem;
      border-radius: 0.5rem;
      display: block;
      height: 1.25rem;
      width: 1.25rem;
      appearance: none;
      border: 2px solid transparent;
      outline: none;
      cursor: pointer;
      transition: all 0.15s ease-in-out;
      box-sizing: border-box;
      flex-shrink: 0;
    }

    .form1-checkbox:focus {
      border-color: #ff8623;
      box-shadow: 0 0 0 3px rgba(255, 134, 35, 0.1);
    }

    .form1-checkbox:checked {
      background-color: #ff8623;
    }

    .form1-submit {
      background-color: #ff8623;
      color: white;
      font-weight: bold;
      padding: 0.5rem 1rem;
      border-radius: 0.5rem;
      margin-top: 0.5rem;
      width: 100%;
      max-width: 100%;
      appearance: none;
      cursor: pointer;
      border: none;
      outline: none;
      transition: all 0.3s ease;
      box-sizing: border-box;
    }

    .form1-submit:hover {
      background-color: #e5751f;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    /*
     * All styles above are scoped with form1- prefix to ensure they only apply
     * to this specific form and don't affect other parts of the website
     */
  </style>
  <body>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
    <form
      id="__vtigerWebForm0292ec2cd1fbe6c16298c27f718adab1"
      class="__vtigerWebForm"
      name="Form 1 UNIVERSAL"
      action="https://microsipmtz.mantic360.net/modules/Webforms/capture.php"
      method="post"
      accept-charset="utf-8"
      enctype="multipart/form-data"
    >
      <input
        type="hidden"
        name="__vtrftk"
        value="sid:09a2a9f048a8226dbf479c498192555a73124d84,1737409522"
      /><input
        type="hidden"
        name="publicid"
        value="0292ec2cd1fbe6c16298c27f718adab1"
      /><input type="hidden" name="urlencodeenable" value="1" /><input
        type="hidden"
        name="name"
        value="Form 1 UNIVERSAL"
      />
      <table class="form1-container">
        <tbody>
          <tr class="form1-row">
            <td>
              <label class="form1-label">¿En qué te podemos ayudar?*</label>
            </td>
            <td>
              <textarea
                class="form1-textarea"
                name="cf_1323"
                required=""
              ></textarea>
            </td>
          </tr>
          <tr class="form1-row">
            <td>
              <label class="form1-label">Nombres*</label>
            </td>
            <td>
              <input
                type="text"
                name="firstname"
                data-label=""
                value=""
                class="form1-input"
                required=""
              />
            </td>
          </tr>
          <tr class="form1-row">
            <td>
              <label class="form1-label">Apellidos*</label>
            </td>
            <td>
              <input
                type="text"
                name="lastname"
                data-label=""
                value=""
                required=""
                class="form1-input"
              />
            </td>
          </tr>
          <tr class="form1-row">
            <td>
              <label class="form1-label">Estado*</label>
            </td>
            <td>
              <select
                name="cf_1315"
                data-label="label:Estado+sitio+web"
                required=""
                class="form1-select"
              >
                <option value="">Seleccione un estado</option>
                <option value="Aguascalientes">Aguascalientes</option>
                <option value="Baja California">Baja California</option>
                <option value="Baja California Sur">Baja California Sur</option>
                <option value="Campeche">Campeche</option>
                <option value="Coahuila">Coahuila</option>
                <option value="Colima">Colima</option>
                <option value="Chiapas">Chiapas</option>
                <option value="Chihuahua">Chihuahua</option>
                <option value="CDMX">CDMX</option>
                <option value="Durango">Durango</option>
                <option value="Guanajuato">Guanajuato</option>
                <option value="Guerrero">Guerrero</option>
                <option value="Hidalgo">Hidalgo</option>
                <option value="Jalisco">Jalisco</option>
                <option value="Edo. de México">Edo. de México</option>
                <option value="Michoacan">Michoacan</option>
                <option value="Morelos">Morelos</option>
                <option value="Nayarit">Nayarit</option>
                <option value="Nuevo León">Nuevo León</option>
                <option value="Oaxaca">Oaxaca</option>
                <option value="Puebla">Puebla</option>
                <option value="Querétaro">Querétaro</option>
                <option value="Quintana Roo">Quintana Roo</option>
                <option value="San Luis Potosí">San Luis Potosí</option>
                <option value="Sinaloa">Sinaloa</option>
                <option value="Sonora">Sonora</option>
                <option value="Tabasco">Tabasco</option>
                <option value="Tamaulipas">Tamaulipas</option>
                <option value="Tlaxcala">Tlaxcala</option>
                <option value="Veracruz">Veracruz</option>
                <option value="Yucatán">Yucatán</option>
                <option value="Zacatecas">Zacatecas</option>
              </select>
            </td>
          </tr>
          <tr class="form1-row">
            <td>
              <label class="form1-label">Municipio o Delegación*</label>
            </td>
            <td>
              <input
                type="text"
                name="cf_1321"
                data-label=""
                value=""
                required=""
                class="form1-input"
              />
            </td>
          </tr>
          <tr class="form1-row">
            <td>
              <label class="form1-label">E-mail*</label>
            </td>
            <td>
              <input
                type="email"
                name="email"
                data-label=""
                value=""
                class="form1-input"
                required=""
              />
            </td>
          </tr>
          <tr class="form1-row">
            <td>
              <label class="form1-label">Tel o Cel*</label>
            </td>
            <td>
              <input
                type="tel"
                name="cf_1129"
                data-label=""
                value=""
                pattern="[0-9]{10}"
                title="Por favor ingrese un número telefónico válido de 10 dígitos"
                class="form1-input"
                required=""
              />
            </td>
          </tr>
          <tr class="form1-row">
            <td>
              <label class="form1-label"
                >¿Usas algún sistema ERP o similar en tu negocio? ¿Si sí,
                cuál?*</label
              >
            </td>
            <td>
              <input
                type="text"
                name="cf_1394"
                data-label=""
                value=""
                required=""
                placeholder=""
                class="form1-input"
              />
            </td>
          </tr>
          <tr class="form1-row-flex">
            <td>
              <label for="soy-usuario" class="form1-label"
                >Soy usuario Microsip</label
              >
            </td>
            <td>
              <input
                type="hidden"
                name="cf_1430"
                data-label=""
                value="0"
              /><input
                type="checkbox"
                name="cf_1430"
                data-label=""
                value="1"
                class="form1-checkbox"
                id="soy-usuario"
              />
            </td>
          </tr>
          <tr class="form1-row-flex">
            <td>
              <label for="recibir" class="form1-label"
                >Recibir Correos de Microsip</label
              >
            </td>
            <td>
              <input
                type="hidden"
                name="cf_1325"
                data-label=""
                value="0"
              /><input
                type="checkbox"
                name="cf_1325"
                data-label=""
                value="1"
                class="form1-checkbox"
                id="recibir"
              />
            </td>
          </tr>
          <tr>
            <td><textarea name="cf_1319" hidden=""></textarea></td>
          </tr>
          <tr>
            <td>
              <!-- Hook for the CRM tracking -->
              <input
                id="hook"
                type="hidden"
                name="cf_1317"
                data-label=""
                value="https://www.microsip.com/your-page"
              />
            </td>
          </tr>
          <tr>
            <td>
              <select
                name="cf_1141"
                data-label="label:Procedencia"
                required=""
                hidden=""
              >
                <option value="">Seleccionar valor</option>
                <option value="Web" selected="">Web</option>
                <option value="Web - Partner">Web - Partner</option>
                <option value="Facebook">Facebook</option>
                <option value="Chat">Chat</option>
                <option value="Tel">Tel</option>
                <option value="<EMAIL>">
                  <EMAIL>
                </option>
                <option value="<EMAIL>"><EMAIL></option>
                <option value="<EMAIL>">
                  <EMAIL>
                </option>
                <option value="Campaña FB">Campaña FB</option>
                <option value="Campaña FB Retargeting">
                  Campaña FB Retargeting
                </option>
                <option value="Campaña Noreste">Campaña Noreste</option>
                <option value="Campaña Preventa">Campaña Preventa</option>
                <option value="Campaña Retargeting">Campaña Retargeting</option>
                <option value="Talk Show">Talk Show</option>
                <option value="En Persona">En Persona</option>
                <option value="Email Mkt">Email Mkt</option>
                <option value="Expo Ferretera 2021">Expo Ferretera 2021</option>
                <option value="Expo Ferretera 2022">Expo Ferretera 2022</option>
                <option value="Expo Ferretera 2023">Expo Ferretera 2023</option>
                <option value="Whatsapp Business">Whatsapp Business</option>
                <option value="Campaña CFDI">Campaña CFDI</option>
                <option value="Campaña FB Golfo">Campaña FB Golfo</option>
                <option value="Campaña Preventa 2023">
                  Campaña Preventa 2023
                </option>
                <option value="Campaña Preventa 2024">
                  Campaña Preventa 2024
                </option>
                <option value="BNI">BNI</option>
                <option value="Campaña generación 2024">
                  Campaña generación 2024
                </option>
                <option value="Blog - Backlink">Blog - Backlink</option>
                <option value="Sin Información">Sin Información</option>
                <option value="Otro">Otro</option>
              </select>
            </td>
          </tr>
          <tr>
            <td>
              <select name="cf_1428" data-label="label:Finalidad" hidden="">
                <option value="">Seleccionar valor</option>
                <option value="Compra" selected="">Compra</option>
                <option value="Distribución">Distribución</option>
                <option value="Soporte">Soporte</option>
                <option value="Error">Error</option>
                <option value="Bolsa de trabajo">Bolsa de trabajo</option>
                <option value="Donativo">Donativo</option>
                <option value="Sugerencia">Sugerencia</option>
                <option value="Otro">Otro</option>
              </select>
            </td>
          </tr>
        </tbody>
      </table>
      <script
        src="https://www.google.com/recaptcha/api.js?hl=es"
        async
        defer
      ></script>
      <div
        class="g-recaptcha"
        data-sitekey="6LfXMKsZAAAAACGRuPAKqie7am6XkoKq0-tZz-aJ"
      ></div>
      <input
        type="hidden"
        id="captchaUrl"
        value="https://microsipmtz.mantic360.net/modules/Settings/Webforms/actions/CheckCaptcha.php"
      /><input
        type="hidden"
        name="recaptcha_validation_value"
        id="recaptcha_validation_value"
        value="false"
      /><input type="submit" value="Enviar" class="form1-submit" />
    </form>
    <script type="text/javascript">
      var _form_active = "";
      window.onload = function () {
        var N = navigator.appName,
          ua = navigator.userAgent,
          tem;
        var M = ua.match(
          /(opera|chrome|safari|firefox|msie)\/?\s*(\.?\d+(\.\d+)*)/i
        );
        if (M && (tem = ua.match(/version\/([\.\d]+)/i)) != null) M[2] = tem[1];
        M = M ? [M[1], M[2]] : [N, navigator.appVersion, "-?"];
        var browserName = M[0];
        var _forms = document.getElementsByClassName("__vtigerWebForm");
        for (var i = 0; i < _forms.length; i++) {
          var form = _forms[i];
          let inputs = form.elements;
          form.onsubmit = function () {
            let _formid = "#" + this.id;
            let _formidname = this.id;
            _form_active = this.id;
            var required = [],
              att,
              val;
            for (var i = 0; i < inputs.length; i++) {
              att = inputs[i].getAttribute("required");
              val = inputs[i].value;
              type = inputs[i].type;
              if (type == "email") {
                if (val != "") {
                  var elemLabel = inputs[i].getAttribute("label");
                  var emailFilter =
                    /^[_/a-zA-Z0-9]+([!"#$%&()*+,./:;<=>?\^_`{|}~-]?[a-zA-Z0-9/_/-])*@[a-zA-Z0-9]+([\_\-\.]?[a-zA-Z0-9]+)*\.([\-\_]?[a-zA-Z0-9])+(\.?[a-zA-Z0-9]+)?$/;
                  var illegalChars = /[\(\)\<\>\,\;\:\"\[\]]/;
                  if (!emailFilter.test(val)) {
                    alert(
                      "For " +
                        elemLabel +
                        " field please enter valid email address"
                    );
                    return false;
                  } else if (val.match(illegalChars)) {
                    alert(elemLabel + " field contains illegal characters");
                    return false;
                  }
                }
              }
              if (att != null) {
                if (val.replace(/^\s+|\s+$/g, "") == "") {
                  required.push(inputs[i].getAttribute("label"));
                }
              }
            }
            if (required.length > 0) {
              alert("The following fields are required: " + required.join());
              return false;
            }
            var numberTypeInputs =
              document.querySelectorAll("input[type=number]");
            for (var i = 0; i < numberTypeInputs.length; i++) {
              val = numberTypeInputs[i].value;
              var elemLabel = numberTypeInputs[i].getAttribute("label");
              var elemDataType = numberTypeInputs[i].getAttribute("datatype");
              if (val != "") {
                if (elemDataType == "double") {
                  var numRegex = /^[+-]?\d+(\.\d+)?$/;
                } else {
                  var numRegex = /^[+-]?\d+$/;
                }
                if (!numRegex.test(val)) {
                  alert(
                    "For " + elemLabel + " field please enter valid number"
                  );
                  return false;
                }
              }
            }
            var dateTypeInputs = document.querySelectorAll("input[type=date]");
            for (var i = 0; i < dateTypeInputs.length; i++) {
              dateVal = dateTypeInputs[i].value;
              var elemLabel = dateTypeInputs[i].getAttribute("label");
              if (dateVal != "") {
                var dateRegex =
                  /^[1-9][0-9]{3}-(0[1-9]|1[0-2]|[1-9]{1})-(0[1-9]|[1-2][0-9]|3[0-1]|[1-9]{1})$/;
                if (!dateRegex.test(dateVal)) {
                  alert(
                    "For " +
                      elemLabel +
                      " field please enter valid date in required format"
                  );
                  return false;
                }
              }
            }
            var inputElems = document.getElementsByTagName("input");
            var totalFileSize = 0;
            for (var i = 0; i < inputElems.length; i++) {
              if (inputElems[i].type.toLowerCase() === "file") {
                var file = inputElems[i].files[0];
                if (typeof file !== "undefined") {
                  var totalFileSize = totalFileSize + file.size;
                }
              }
            }
            if (totalFileSize > 52428800) {
              alert("Maximum allowed file size including all files is 50MB.");
              return false;
            }
            var getStatus = false;
            var recaptchaValidationValue = document.querySelector(
              _formid + " .g-recaptcha .g-recaptcha-response"
            ).value;
            var captchaUrl = document.querySelector(
              _formid + " #captchaUrl"
            ).value;
            var url =
              captchaUrl +
              "?form=" +
              _formidname +
              "&recaptcha_response=" +
              recaptchaValidationValue;
            url = url + "&callback=JSONPCallback";
            jsonp.fetch(url);
            if (getStatus == false) {
              return false;
            }
          };
        }
      };
      var jsonp = {
        callbackCounter: 0,
        fetch: function (url) {
          url = url + "&callId=" + this.callbackCounter;
          var scriptTag = document.createElement("SCRIPT");
          scriptTag.src = url;
          scriptTag.async = true;
          scriptTag.id = "JSONPCallback_" + this.callbackCounter;
          scriptTag.type = "text/javascript";
          document.getElementsByTagName("HEAD")[0].appendChild(scriptTag);
          this.callbackCounter++;
        },
      };
      function JSONPCallback(data) {
        if (data.result.success == true) {
          document.querySelector(
            "#" + _form_active + " #recaptcha_validation_value"
          ).value = true;
          var form = document.getElementById(_form_active);
          form.submit();
        } else {
          document.querySelector(
            "#" + _form_active + " #recaptcha_validation_value"
          ).value = false;
          alert("Bot detectado");
        }
        var element = document.getElementById(
          "JSONPCallback_" + data.result.callId
        );
        element.parentNode.removeChild(element);
      }
    </script>

    <!-- URL Auto-Population Script -->
    <script type="text/javascript">
      // Automatically populate the CRM tracking hook with the current page URL
      document.addEventListener("DOMContentLoaded", function () {
        // Get the current page URL
        var currentUrl = window.location.href;

        // Find the hidden input field using both possible selectors for reliability
        var hookInput =
          document.querySelector('input[name="cf_1317"]') ||
          document.getElementById("hook");

        if (hookInput) {
          // Set the value to the current URL, replacing the placeholder
          hookInput.value = currentUrl;
          // console.log("CRM tracking hook populated with URL:", currentUrl);
        } else {
          // console.warn("CRM tracking hook input field not found");
        }
      });
    </script>
  </body>
</html>
